<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Design System Playground</title>
    <link rel="stylesheet" href="../design-tokens.css">
    <link rel="stylesheet" href="../components/ui/status-bar/status-bar.css">
    <link rel="stylesheet" href="../components/ui/button/button.css">
    <link rel="stylesheet" href="../components/ui/card/card.css">
    <link rel="stylesheet" href="../components/ui/navigation/navigation.css">
    <link rel="stylesheet" href="../components/ui/grid/grid.css">
    <style>
        .playground {
            min-height: 100vh;
            background: var(--color-background-primary);
            padding: var(--spacing-xl);
        }

        .playground__header {
            text-align: center;
            margin-bottom: var(--spacing-4xl);
            padding-bottom: var(--spacing-xl);
            border-bottom: 1px solid var(--color-border-primary);
        }

        .playground__title {
            font-size: var(--font-size-4xl);
            font-weight: var(--font-weight-bold);
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-md);
        }

        .playground__subtitle {
            font-size: var(--font-size-lg);
            color: var(--color-text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .component-section {
            margin-bottom: var(--spacing-4xl);
            background: var(--color-surface-primary);
            border-radius: var(--radius-xl);
            padding: var(--spacing-2xl);
        }

        .component-section__header {
            margin-bottom: var(--spacing-2xl);
            padding-bottom: var(--spacing-lg);
            border-bottom: 1px solid var(--color-border-secondary);
        }

        .component-section__title {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .component-section__description {
            font-size: var(--font-size-base);
            color: var(--color-text-secondary);
            line-height: var(--line-height-normal);
        }

        .component-demo {
            background: var(--color-background-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }

        .component-demo__title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-lg);
        }

        .component-demo__content {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-lg);
            align-items: flex-start;
        }

        .demo-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .demo-group--horizontal {
            flex-direction: row;
            align-items: center;
        }

        .phone-mockup {
            width: 375px;
            height: 600px;
            background: var(--color-background-primary);
            border-radius: var(--radius-2xl);
            border: 3px solid var(--color-border-primary);
            overflow: hidden;
            position: relative;
            margin: 0 auto;
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-md);
            margin: var(--spacing-lg) 0;
        }

        .color-swatch {
            text-align: center;
        }

        .color-swatch__color {
            width: 100%;
            height: 60px;
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-sm);
            border: 1px solid var(--color-border-secondary);
        }

        .color-swatch__name {
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
            margin-bottom: var(--spacing-xs);
        }

        .color-swatch__value {
            font-size: var(--font-size-xs);
            color: var(--color-text-tertiary);
            font-family: var(--font-family-mono);
        }

        .typography-demo {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }

        .typography-sample {
            padding: var(--spacing-md);
            background: var(--color-background-secondary);
            border-radius: var(--radius-md);
        }

        .typography-sample__label {
            font-size: var(--font-size-xs);
            color: var(--color-text-tertiary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: var(--spacing-xs);
        }

        .spacing-demo {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-lg);
        }

        .spacing-sample {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .spacing-sample__box {
            background: var(--color-primary);
            border-radius: var(--radius-sm);
        }

        .spacing-sample__label {
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
            font-family: var(--font-family-mono);
        }

        .toc {
            position: fixed;
            top: var(--spacing-xl);
            right: var(--spacing-xl);
            background: var(--color-surface-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            border: 1px solid var(--color-border-primary);
            max-width: 200px;
            z-index: var(--z-sticky);
        }

        .toc__title {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-semibold);
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-md);
        }

        .toc__list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .toc__item {
            margin-bottom: var(--spacing-xs);
        }

        .toc__link {
            color: var(--color-text-secondary);
            text-decoration: none;
            font-size: var(--font-size-sm);
            transition: var(--transition-fast);
        }

        .toc__link:hover {
            color: var(--color-primary);
        }

        @media (max-width: 1200px) {
            .toc {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .playground {
                padding: var(--spacing-lg);
            }
            
            .component-section {
                padding: var(--spacing-lg);
            }
            
            .phone-mockup {
                width: 100%;
                max-width: 375px;
            }
            
            .component-demo__content {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="playground">
        <!-- Table of Contents -->
        <nav class="toc">
            <h3 class="toc__title">Components</h3>
            <ul class="toc__list">
                <li class="toc__item"><a href="#design-tokens" class="toc__link">Design Tokens</a></li>
                <li class="toc__item"><a href="#status-bar" class="toc__link">Status Bar</a></li>
                <li class="toc__item"><a href="#buttons" class="toc__link">Buttons</a></li>
                <li class="toc__item"><a href="#cards" class="toc__link">Cards</a></li>
                <li class="toc__item"><a href="#navigation" class="toc__link">Navigation</a></li>
                <li class="toc__item"><a href="#grids" class="toc__link">Grids</a></li>
                <li class="toc__item"><a href="#complete-example" class="toc__link">Complete Example</a></li>
            </ul>
        </nav>

        <!-- Header -->
        <header class="playground__header">
            <h1 class="playground__title">Design System Playground</h1>
            <p class="playground__subtitle">
                Explore our comprehensive design system with reusable components, 
                design tokens, and interactive examples. Built with DRY principles 
                and modern web standards.
            </p>
        </header>

        <!-- Design Tokens Section -->
        <section id="design-tokens" class="component-section">
            <div class="component-section__header">
                <h2 class="component-section__title">Design Tokens</h2>
                <p class="component-section__description">
                    Foundation of our design system - colors, typography, spacing, and more.
                </p>
            </div>

            <!-- Colors -->
            <div class="component-demo">
                <h3 class="component-demo__title">Color Palette</h3>
                <div class="color-palette">
                    <div class="color-swatch">
                        <div class="color-swatch__color" style="background: var(--color-primary);"></div>
                        <div class="color-swatch__name">Primary</div>
                        <div class="color-swatch__value">#30d158</div>
                    </div>
                    <div class="color-swatch">
                        <div class="color-swatch__color" style="background: var(--color-secondary);"></div>
                        <div class="color-swatch__name">Secondary</div>
                        <div class="color-swatch__value">#007aff</div>
                    </div>
                    <div class="color-swatch">
                        <div class="color-swatch__color" style="background: var(--color-background-primary);"></div>
                        <div class="color-swatch__name">Background</div>
                        <div class="color-swatch__value">#000000</div>
                    </div>
                    <div class="color-swatch">
                        <div class="color-swatch__color" style="background: var(--color-surface-primary);"></div>
                        <div class="color-swatch__name">Surface</div>
                        <div class="color-swatch__value">#1c1c1e</div>
                    </div>
                    <div class="color-swatch">
                        <div class="color-swatch__color" style="background: var(--color-text-primary);"></div>
                        <div class="color-swatch__name">Text Primary</div>
                        <div class="color-swatch__value">#ffffff</div>
                    </div>
                    <div class="color-swatch">
                        <div class="color-swatch__color" style="background: var(--color-text-secondary);"></div>
                        <div class="color-swatch__name">Text Secondary</div>
                        <div class="color-swatch__value">#999999</div>
                    </div>
                </div>
            </div>

            <!-- Typography -->
            <div class="component-demo">
                <h3 class="component-demo__title">Typography</h3>
                <div class="typography-demo">
                    <div class="typography-sample">
                        <div class="typography-sample__label">Heading 1 (4xl)</div>
                        <h1 style="font-size: var(--font-size-4xl); margin: 0;">The quick brown fox</h1>
                    </div>
                    <div class="typography-sample">
                        <div class="typography-sample__label">Heading 2 (2xl)</div>
                        <h2 style="font-size: var(--font-size-2xl); margin: 0;">The quick brown fox</h2>
                    </div>
                    <div class="typography-sample">
                        <div class="typography-sample__label">Body (base)</div>
                        <p style="font-size: var(--font-size-base); margin: 0;">The quick brown fox jumps over the lazy dog</p>
                    </div>
                    <div class="typography-sample">
                        <div class="typography-sample__label">Small (sm)</div>
                        <p style="font-size: var(--font-size-sm); margin: 0;">The quick brown fox jumps over the lazy dog</p>
                    </div>
                </div>
            </div>

            <!-- Spacing -->
            <div class="component-demo">
                <h3 class="component-demo__title">Spacing Scale</h3>
                <div class="spacing-demo">
                    <div class="spacing-sample">
                        <div class="spacing-sample__box" style="width: var(--spacing-xs); height: 20px;"></div>
                        <span class="spacing-sample__label">xs (4px)</span>
                    </div>
                    <div class="spacing-sample">
                        <div class="spacing-sample__box" style="width: var(--spacing-sm); height: 20px;"></div>
                        <span class="spacing-sample__label">sm (8px)</span>
                    </div>
                    <div class="spacing-sample">
                        <div class="spacing-sample__box" style="width: var(--spacing-md); height: 20px;"></div>
                        <span class="spacing-sample__label">md (12px)</span>
                    </div>
                    <div class="spacing-sample">
                        <div class="spacing-sample__box" style="width: var(--spacing-lg); height: 20px;"></div>
                        <span class="spacing-sample__label">lg (15px)</span>
                    </div>
                    <div class="spacing-sample">
                        <div class="spacing-sample__box" style="width: var(--spacing-xl); height: 20px;"></div>
                        <span class="spacing-sample__label">xl (20px)</span>
                    </div>
                    <div class="spacing-sample">
                        <div class="spacing-sample__box" style="width: var(--spacing-2xl); height: 20px;"></div>
                        <span class="spacing-sample__label">2xl (24px)</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Status Bar Section -->
        <section id="status-bar" class="component-section">
            <div class="component-section__header">
                <h2 class="component-section__title">Status Bar</h2>
                <p class="component-section__description">
                    Mobile status bar component with time, signal, WiFi, and battery indicators.
                </p>
            </div>

            <div class="component-demo">
                <h3 class="component-demo__title">Status Bar Variants</h3>
                <div class="component-demo__content">
                    <div class="demo-group">
                        <div id="status-bar-demo-1"></div>
                        <div id="status-bar-demo-2"></div>
                        <div id="status-bar-demo-3"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Buttons Section -->
        <section id="buttons" class="component-section">
            <div class="component-section__header">
                <h2 class="component-section__title">Buttons</h2>
                <p class="component-section__description">
                    Versatile button component with multiple variants, sizes, and states.
                </p>
            </div>

            <div class="component-demo">
                <h3 class="component-demo__title">Button Variants</h3>
                <div class="component-demo__content">
                    <div class="demo-group demo-group--horizontal">
                        <div id="button-primary"></div>
                        <div id="button-secondary"></div>
                        <div id="button-ghost"></div>
                        <div id="button-outline"></div>
                    </div>
                </div>
            </div>

            <div class="component-demo">
                <h3 class="component-demo__title">Button Sizes</h3>
                <div class="component-demo__content">
                    <div class="demo-group demo-group--horizontal">
                        <div id="button-xs"></div>
                        <div id="button-sm"></div>
                        <div id="button-md"></div>
                        <div id="button-lg"></div>
                    </div>
                </div>
            </div>

            <div class="component-demo">
                <h3 class="component-demo__title">Button States</h3>
                <div class="component-demo__content">
                    <div class="demo-group demo-group--horizontal">
                        <div id="button-loading"></div>
                        <div id="button-disabled"></div>
                        <div id="button-icon"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Cards Section -->
        <section id="cards" class="component-section">
            <div class="component-section__header">
                <h2 class="component-section__title">Cards</h2>
                <p class="component-section__description">
                    Flexible card component for displaying content with various layouts and styles.
                </p>
            </div>

            <div class="component-demo">
                <h3 class="component-demo__title">Music Cards</h3>
                <div class="component-demo__content">
                    <div class="grid grid--cols-2 grid--gap-md" style="max-width: 400px;">
                        <div id="music-card-1"></div>
                        <div id="music-card-2"></div>
                        <div id="music-card-3"></div>
                        <div id="music-card-4"></div>
                    </div>
                </div>
            </div>

            <div class="component-demo">
                <h3 class="component-demo__title">Featured Card</h3>
                <div class="component-demo__content">
                    <div style="max-width: 500px;">
                        <div id="featured-card"></div>
                    </div>
                </div>
            </div>

            <div class="component-demo">
                <h3 class="component-demo__title">Release Cards</h3>
                <div class="component-demo__content">
                    <div class="release-grid">
                        <div id="release-card-1"></div>
                        <div id="release-card-2"></div>
                        <div id="release-card-3"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Navigation Section -->
        <section id="navigation" class="component-section">
            <div class="component-section__header">
                <h2 class="component-section__title">Navigation</h2>
                <p class="component-section__description">
                    Navigation components including tabs, bottom navigation, and breadcrumbs.
                </p>
            </div>

            <div class="component-demo">
                <h3 class="component-demo__title">Tab Navigation</h3>
                <div class="component-demo__content">
                    <div id="tab-navigation"></div>
                </div>
            </div>

            <div class="component-demo">
                <h3 class="component-demo__title">Bottom Navigation</h3>
                <div class="component-demo__content">
                    <div class="phone-mockup">
                        <div id="bottom-navigation"></div>
                    </div>
                </div>
            </div>

            <div class="component-demo">
                <h3 class="component-demo__title">Breadcrumb</h3>
                <div class="component-demo__content">
                    <div id="breadcrumb-navigation"></div>
                </div>
            </div>
        </section>

        <!-- Grids Section -->
        <section id="grids" class="component-section">
            <div class="component-section__header">
                <h2 class="component-section__title">Grids</h2>
                <p class="component-section__description">
                    Responsive grid system for organizing content in various layouts.
                </p>
            </div>

            <div class="component-demo">
                <h3 class="component-demo__title">Music Grid</h3>
                <div class="component-demo__content">
                    <div id="music-grid-demo"></div>
                </div>
            </div>

            <div class="component-demo">
                <h3 class="component-demo__title">Release Grid</h3>
                <div class="component-demo__content">
                    <div id="release-grid-demo"></div>
                </div>
            </div>

            <div class="component-demo">
                <h3 class="component-demo__title">Responsive Grid</h3>
                <div class="component-demo__content">
                    <div id="responsive-grid-demo"></div>
                </div>
            </div>
        </section>

        <!-- Complete Example Section -->
        <section id="complete-example" class="component-section">
            <div class="component-section__header">
                <h2 class="component-section__title">Complete Example</h2>
                <p class="component-section__description">
                    A complete music app interface using all components together.
                </p>
            </div>

            <div class="component-demo">
                <h3 class="component-demo__title">Music App Interface</h3>
                <div class="component-demo__content">
                    <div class="phone-mockup">
                        <div id="complete-app"></div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Scripts -->
    <script src="../components/ui/status-bar/status-bar.js"></script>
    <script src="../components/ui/button/button.js"></script>
    <script src="../components/ui/card/card.js"></script>
    <script src="../components/ui/navigation/navigation.js"></script>
    <script src="../components/ui/grid/grid.js"></script>
    
    <script>
        // Initialize Status Bar demos
        new StatusBar({
            time: '9:41',
            batteryLevel: 85,
            signalStrength: 'strong',
            wifiConnected: true,
            autoUpdate: false
        }).mount('#status-bar-demo-1');

        new StatusBar({
            time: '9:41',
            batteryLevel: 20,
            signalStrength: 'weak',
            wifiConnected: false,
            autoUpdate: false
        }).mount('#status-bar-demo-2');

        new StatusBar({
            time: '9:41',
            batteryLevel: 45,
            signalStrength: 'medium',
            wifiConnected: true,
            isCharging: true,
            autoUpdate: false
        }).mount('#status-bar-demo-3');

        // Initialize Button demos
        new Button({
            text: 'Primary',
            variant: 'primary'
        }).mount('#button-primary');

        new Button({
            text: 'Secondary',
            variant: 'secondary'
        }).mount('#button-secondary');

        new Button({
            text: 'Ghost',
            variant: 'ghost'
        }).mount('#button-ghost');

        new Button({
            text: 'Outline',
            variant: 'outline'
        }).mount('#button-outline');

        // Button sizes
        new Button({ text: 'XS', size: 'xs' }).mount('#button-xs');
        new Button({ text: 'Small', size: 'sm' }).mount('#button-sm');
        new Button({ text: 'Medium', size: 'md' }).mount('#button-md');
        new Button({ text: 'Large', size: 'lg' }).mount('#button-lg');

        // Button states
        new Button({ text: 'Loading', loading: true }).mount('#button-loading');
        new Button({ text: 'Disabled', disabled: true }).mount('#button-disabled');
        new Button({
            text: 'With Icon',
            icon: '🎵',
            iconPosition: 'left'
        }).mount('#button-icon');

        // Initialize Card demos
        Card.createMusicCard({
            icon: '🎵',
            title: 'Brat and it\'s completely diff...',
            backgroundColor: '#4CAF50'
        }).mount('#music-card-1');

        Card.createMusicCard({
            icon: '🎭',
            title: 'Wicked Official Playlist',
            backgroundColor: '#FF5722'
        }).mount('#music-card-2');

        Card.createMusicCard({
            icon: '👤',
            title: 'Gracie Abrams',
            backgroundColor: '#9C27B0'
        }).mount('#music-card-3');

        Card.createMusicCard({
            icon: '🎸',
            title: 'More Life',
            backgroundColor: '#FF9800'
        }).mount('#music-card-4');

        Card.createFeaturedCard({
            avatar: '🎙️',
            badge: 'Podcast',
            title: 'Sounds Like A Cult',
            description: 'A podcast about the modern-day "cults" we all follow.',
            backgroundColor: 'linear-gradient(45deg, #FF6B6B, #4ECDC4)'
        }).mount('#featured-card');

        Card.createReleaseCard({
            title: 'New Album',
            backgroundColor: 'linear-gradient(45deg, #FFD700, #FFA500)'
        }).mount('#release-card-1');

        Card.createReleaseCard({
            title: 'EP Release',
            backgroundColor: 'linear-gradient(45deg, #00CED1, #1E90FF)'
        }).mount('#release-card-2');

        Card.createReleaseCard({
            title: 'Single',
            backgroundColor: 'linear-gradient(45deg, #DDA0DD, #9370DB)'
        }).mount('#release-card-3');

        // Initialize Navigation demos
        new TabNavigation({
            tabs: [
                { label: 'T' },
                { label: 'All' },
                { label: 'Music' },
                { label: 'Podcasts' },
                { label: 'Audiobooks' }
            ],
            activeTab: 1,
            onChange: (index, tab) => {
                console.log('Tab changed:', index, tab);
            }
        }).mount('#tab-navigation');

        new BottomNavigation({
            items: [
                { icon: 'home', label: 'Home' },
                { icon: 'search', label: 'Search' },
                { icon: 'library', label: 'Your Library', badge: 3 },
                { icon: 'create', label: 'Create' }
            ],
            activeItem: 0,
            position: 'relative',
            onChange: (index, item) => {
                console.log('Nav item changed:', index, item);
            }
        }).mount('#bottom-navigation');

        new Breadcrumb({
            items: [
                { label: 'Home', href: '#' },
                { label: 'Music', href: '#' },
                { label: 'Artists', href: '#' },
                { label: 'Taylor Swift' }
            ],
            onClick: (index, item) => {
                console.log('Breadcrumb clicked:', index, item);
            }
        }).mount('#breadcrumb-navigation');

        // Initialize Grid demos
        const musicGridItems = [
            { title: 'Brat and it\'s completely diff...', icon: '🎵', color: '#4CAF50' },
            { title: 'Wicked Official Playlist', icon: '🎭', color: '#FF5722' },
            { title: 'Gracie Abrams', icon: '👤', color: '#9C27B0' },
            { title: 'More Life', icon: '🎸', color: '#FF9800' },
            { title: 'DJ', icon: '🎧', color: '#2196F3' },
            { title: 'Today\'s Top Hits', icon: '🎵', color: '#607D8B' }
        ];

        Grid.createMusicGrid(musicGridItems, {
            columns: 2,
            itemRenderer: (item, index) => {
                return `
                    <div class="card card--music">
                        <div class="card__avatar" style="background: ${item.color};">
                            ${item.icon}
                        </div>
                        <h3 class="card__title">${item.title}</h3>
                    </div>
                `;
            }
        }).mount('#music-grid-demo');

        const releaseGridItems = [
            { title: 'Album 1', color: 'linear-gradient(45deg, #FFD700, #FFA500)' },
            { title: 'Album 2', color: 'linear-gradient(45deg, #00CED1, #1E90FF)' },
            { title: 'Album 3', color: 'linear-gradient(45deg, #DDA0DD, #9370DB)' },
            { title: 'Album 4', color: 'linear-gradient(45deg, #FF6B6B, #4ECDC4)' }
        ];

        Grid.createReleaseGrid(releaseGridItems, {
            itemRenderer: (item, index) => {
                return `
                    <div class="card card--release">
                        <div class="card__image" style="background: ${item.color};"></div>
                        <h3 class="card__title">${item.title}</h3>
                    </div>
                `;
            }
        }).mount('#release-grid-demo');

        const responsiveGridItems = Array.from({ length: 8 }, (_, i) => ({
            title: `Item ${i + 1}`,
            content: `Content for item ${i + 1}`
        }));

        new Grid({
            items: responsiveGridItems,
            columns: 4,
            gap: 'md',
            responsive: true,
            itemRenderer: (item, index) => {
                return `
                    <div class="card">
                        <h3 class="card__title">${item.title}</h3>
                        <p>${item.content}</p>
                    </div>
                `;
            }
        }).mount('#responsive-grid-demo');

        // Complete App Example
        const completeAppContainer = document.getElementById('complete-app');

        // Status Bar
        const statusBar = new StatusBar({
            time: '9:41',
            batteryLevel: 80,
            signalStrength: 'strong',
            wifiConnected: true,
            autoUpdate: false
        });

        // Tab Navigation
        const tabNav = new TabNavigation({
            tabs: ['T', 'All', 'Music', 'Podcasts'],
            activeTab: 1,
            size: 'sm'
        });

        // Music Grid
        const musicGrid = Grid.createMusicGrid(musicGridItems.slice(0, 4), {
            columns: 2,
            gap: 'md',
            itemRenderer: (item, index) => {
                return `
                    <div class="card card--music">
                        <div class="card__avatar" style="background: ${item.color};">
                            ${item.icon}
                        </div>
                        <h3 class="card__title">${item.title}</h3>
                    </div>
                `;
            }
        });

        // Bottom Navigation
        const bottomNav = new BottomNavigation({
            items: [
                { icon: 'home', label: 'Home' },
                { icon: 'search', label: 'Search' },
                { icon: 'library', label: 'Library' },
                { icon: 'create', label: 'Create' }
            ],
            activeItem: 0,
            position: 'relative'
        });

        // Assemble complete app
        completeAppContainer.innerHTML = `
            <div style="height: 100%; display: flex; flex-direction: column;">
                <div id="complete-status-bar"></div>
                <div id="complete-tab-nav"></div>
                <div style="flex: 1; padding: 0 20px; overflow-y: auto;">
                    <h2 style="font-size: 22px; font-weight: 700; margin-bottom: 15px; color: white;">Your Music</h2>
                    <div id="complete-music-grid"></div>
                </div>
                <div id="complete-bottom-nav"></div>
            </div>
        `;

        statusBar.mount('#complete-status-bar');
        tabNav.mount('#complete-tab-nav');
        musicGrid.mount('#complete-music-grid');
        bottomNav.mount('#complete-bottom-nav');

        // Smooth scrolling for TOC links
        document.querySelectorAll('.toc__link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(link.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    </script>
</body>
</html>
